// Zentrale Konfiguration
const CONFIG = {
  SPREADSHEET_ID: '1qsGF8eQY8WuV-HTETep78VUsylGgox2jdvdtFqZhjiw',
  SHEETS: {
    BAR: 'BarSchichten',
    POMMES: 'PommesSchichten',
    SPUEL: 'SpuelSchichten',
    KINDERTOEPFERN: 'Kindertoepfern',
    LEBENDWERKSTATT: 'Lebendwerkstatt',
    SIEBDRUCK: 'Siebdruck',
    AUSSTELLUNG: 'Ausstellungsaufsicht',
    KUCHEN: 'Kuchenspenden',
    PLAKATE: 'Plakate',
    AUFBAU: 'Aufbauhelfer',
    LINEUP: 'LineUp',
    ZUSTAENDIGE: '<PERSON>ustaendigePersonen'
  }
};

function doGet(e) {
  const liste = e.parameter.liste || null;
  const template = HtmlService.createTemplateFromFile('index');
  template.LISTE = liste;
  template.PARAMS = e.parameter;
  
  return template
    .evaluate()
    .setTitle('Markt_Listen')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .addMetaTag('viewport', 'width=device-width, initial-scale=1');
}

function include(filename) {
  if (!filename) return '';
  try {
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    console.error('Fehler beim Laden des Templates:', filename, error);
    return '';
  }
}

function getSheetByName(sheetName) {
  try {
    return SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID).getSheetByName(sheetName);
  } catch (error) {
    console.error('Fehler beim Abrufen des Sheets:', error);
    return null;
  }
}

function getConfig() {
  return { SHEETS: CONFIG.SHEETS };
}

function getListe(sheetName) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) return null;
  
  try {
    const values = sheet.getDataRange().getValues();
    const [headers, ...data] = values.slice(3);
    
    // Kommentare nur für Spül-Schichten laden
    const comments = sheetName === CONFIG.SHEETS.SPUEL ? 
      data.map((row, index) => {
        try {
          return sheet.getRange(index + 5, 1).getNote() || '';
        } catch (error) {
          return '';
        }
      }) : [];
    
    return {
      headers,
      data,
      comments,
      sheetName,
      teamleiter: values[0][1],
      unterueberschrift: values[1][1]
    };
  } catch (error) {
    console.error('Fehler in getListe:', error);
    return null;
  }
}

function updateListe(sheetName, rowIndex, columnIndex, value) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) return false;
  
  try {
    sheet.getRange(rowIndex + 5, columnIndex + 1).setValue(value);
    return true;
  } catch (error) {
    console.error('Fehler beim Aktualisieren:', error);
    return false;
  }
}

function addEintrag(sheetName, values) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) throw new Error('Sheet nicht gefunden: ' + sheetName);
  sheet.appendRow(values);
}


