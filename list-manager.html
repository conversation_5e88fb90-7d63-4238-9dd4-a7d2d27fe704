<script>
  // Konfigurationen für alle Listen-Typen
  const LIST_CONFIGS = {
    // VOR MARKT - Listen mit dynamischem Hinzufügen von Einträgen
    'plakate': {
      title: 'Plakate',
      sheet: 'PLAKATE',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Ort eingeben' },  // Ort
        { editable: true, placeholder: 'Name eingeben' },  // Person
        { editable: true, placeholder: 'Status' }  // Erledigt
      ]
    },
    'kuchen': {
      title: 'Kuchenspenden',
      sheet: 'KUCHEN',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Name
        { editable: true, type: 'dropdown', options: ['', 'Fr', 'Sa', 'So'], placeholder: 'Tag wählen' },  // Tag
        { editable: true, placeholder: 'Info eingeben (optional)' }  // Info
      ]
    },
    'aufbau': {
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      sheet: 'AUFBAU',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Name
        { editable: true, placeholder: 'Mo' },  // Mo
        { editable: true, placeholder: 'Di' },  // Di
        { editable: true, placeholder: 'Mi' },  // Mi
        { editable: true, placeholder: 'Do' },  // Do
        { editable: true, placeholder: 'Fr' }   // Fr
      ]
    },
    'zustaendige': {
      title: 'Zuständige Personen',
      sheet: 'ZUSTAENDIGE',
      type: 'sonstigeInfos',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Zuständige Person
        { editable: true, placeholder: 'Bereich/Aufgabe eingeben' }  // Bereich/Aufgabe
      ]
    },
  
    // WÄHREND MARKT - Schichtlisten mit festen Zeiten
    'bar': {
      title: 'Bar-Schichten',
      sheet: 'BAR',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }, // Person 2
        { editable: true, placeholder: 'Name eingeben' }, // Kaffee
        { editable: true, placeholder: 'Name eingeben' }  // Bereitschaft
      ]
    },
    'pommes': {
      title: 'Pommes-Schichten',
      sheet: 'POMMES',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'spuel': {
      title: 'Spül-Schichten',
      sheet: 'SPUEL',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'kindertoepfern': {
      title: 'Kindertöpfern (KSL)',
      sheet: 'KINDERTOEPFERN',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'lebendwerkstatt': {
      title: 'Lebendwerkstatt (KSL)',
      sheet: 'LEBENDWERKSTATT',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'siebdruck': {
      title: 'Siebdruck (KSL)',
      sheet: 'SIEBDRUCK',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'ausstellung': {
      title: 'Ausstellungsaufsicht',
      sheet: 'AUSSTELLUNG',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }  // Person
      ]
    },
    'lineup': {
      title: 'LineUp',
      sheet: 'LINEUP',
      type: 'sonstigeInfos',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: false }, // Künstler:innen/ Bandnamen
        { editable: false }  // Genre
      ]
    }
  };
  
  // Generische Funktion zum Laden aller Listen
  function loadListeByType(type) {
    if (!CONFIG) {
      showError('Konfiguration nicht geladen. Bitte Seite neu laden.');
      return;
    }

    const config = LIST_CONFIGS[type];
    if (!config) {
      showError('Ungültiger Listentyp');
      return;
    }

    updateUrlForList(type);
    const sheetName = CONFIG.SHEETS[config.sheet];

    // Prüfe Cache zuerst
    const cacheKey = `liste_${type}`;
    const cachedData = getCachedData(cacheKey);

    if (cachedData) {
      displayListe(cachedData, config.title, config.columns, config.type);
      // Lade andere Listen im Hintergrund
      preloadRemainingLists(type);
      return;
    }

    // Lade Daten vom Server
    loadListe(config.title, sheetName, config.columns, config.type, cacheKey);
    
    // Lade andere Listen im Hintergrund
    preloadRemainingLists(type);
  }
  
  // Funktion zum Aktualisieren der URL
  function updateUrlForList(listType) {
    const url = new URL(window.location);
    url.searchParams.set('liste', listType);
    window.history.pushState({}, '', url);
  }
  
  // Funktion zum Laden einer Liste
  function loadListe(title, sheetName, columnConfig, listType, cacheKey) {
    if (!sheetName || !columnConfig) {
      showError('Ungültige Parameter');
      return;
    }

    showLoading();

    google.script.run
      .withSuccessHandler(function(data) {
        if (!data || !data.data) {
          showError('Keine Daten gefunden für ' + sheetName);
          return;
        }

        // Cache die Daten
        if (cacheKey) {
          setCachedData(cacheKey, data);
        }

        displayListe(data, title, columnConfig, listType);
      })
      .withFailureHandler(function(error) {
        showError('Fehler beim Laden der Liste: ' + error);
      })
      .getListe(sheetName);
  }
  
  // Generische Funktion zum Anzeigen einer Liste
  function displayListe(data, title, columnConfig, listType) {
    const content = document.getElementById('content');
    if (!content) return;

    // Für Schichtlisten gruppieren wir nach Tagen
    if (listType === 'waehrendMarkt' || data.sheetName === CONFIG.SHEETS.LINEUP) {
      displayGroupedListe(data, title, listType, content);
    } else {
      displaySimpleListe(data, title, columnConfig, listType, content);
    }

    M.AutoInit();
  }

  // Gruppierte Listen (Schichtlisten)
  function displayGroupedListe(data, title, listType, content) {
    // Gruppiere die Daten nach Tagen
    const groupedData = {};
    data.data.forEach((row, index) => {
      const tag = row[0];
      if (!groupedData[tag]) {
        groupedData[tag] = [];
      }
      const rowWithComment = {
        data: row,
        comment: data.comments && data.comments[index] ? data.comments[index] : '',
        originalIndex: index
      };
      groupedData[tag].push(rowWithComment);
    });

    // Erstelle die HTML-Struktur
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          ${Object.entries(groupedData).map(([tag, rows]) => `
            <div class="section">
              <h5 class="teal-text">${tag}</h5>
              <div class="table-container">
                <table class="striped">
                  <thead>
                    <tr>
                      ${data.headers.slice(1).map(header => `<th>${header}</th>`).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${rows.map((rowObj, rowIndex) => {
                      const row = rowObj.data;
                      const comment = rowObj.comment;
                      const originalIndex = rowObj.originalIndex;
                      
                      let tr = document.createElement('tr');
                      tr.innerHTML = row.slice(1).map((cell, colIndex) => {
                        // Für LineUp nur Text anzeigen
                        if (data.sheetName === CONFIG.SHEETS.LINEUP) {
                          return `<td>${cell}</td>`;
                        }
                        // Zeit-Spalte ist nicht editierbar
                        if (colIndex === 0) {
                          return `<td>${cell}</td>`;
                        }
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}" 
                                   onchange="updateListe('${data.sheetName}', ${originalIndex}, ${colIndex + 1}, this.value)">
                          </div>
                        </td>`;
                      }).join('');
                      
                      let html = tr.outerHTML;
                      
                      // Füge Kommentar-Zeile hinzu, falls vorhanden (unter der Schichtzeile)
                      if (comment && data.sheetName === CONFIG.SHEETS.SPUEL) {
                        const commentRow = document.createElement('tr');
                        commentRow.className = 'comment-row';
                        commentRow.innerHTML = `
                          <td colspan="${data.headers.length - 1}" class="comment-cell">
                            <div class="chip teal lighten-4 teal-text text-darken-2">
                              <i class="material-icons tiny">info</i>
                              ${comment}
                            </div>
                          </td>
                        `;
                        html += commentRow.outerHTML;
                      }
                      
                      return html;
                    }).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  // Einfache Listen
  function displaySimpleListe(data, title, columnConfig, listType, content) {
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          <div class="table-container">
            <table class="striped">
              <thead>
                <tr>
                  ${listType === 'vorMarkt' ?
                    data.headers.slice(1).map(header => `<th>${header}</th>`).join('') :
                    data.headers.map(header => `<th>${header}</th>`).join('')
                  }
                </tr>
              </thead>
              <tbody>
                ${data.data.map((row, rowIndex) => {
                  const tr = document.createElement('tr');
                  // Für vorMarkt Listen: überspringe die erste Spalte (Nr)
                  const displayRow = listType === 'vorMarkt' ? row.slice(1) : row;
                  tr.innerHTML = displayRow.map((cell, colIndex) => {
                    // Für sonstige Infos nur Text anzeigen
                    if (listType === 'sonstigeInfos') {
                      return `<td>${cell}</td>`;
                    }

                    // Für Schichtlisten (während Markt)
                    if (listType === 'waehrendMarkt') {
                      // Erste zwei Spalten (Tag und Zeit) sind nicht editierbar
                      if (colIndex < 2) {
                        return `<td>${cell}</td>`;
                      }
                      return `<td>
                        <div class="input-field">
                          <input type="text" value="${cell}"
                                 onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                        </div>
                      </td>`;
                    }

                    // Für Listen vor dem Markt
                    if (listType === 'vorMarkt') {
                      // Anpassung der colIndex für Google Sheet Spalten (da wir die Nr-Spalte übersprungen haben)
                      const actualColIndex = colIndex + 2; // +2 weil: colIndex 0-basiert + Nr-Spalte übersprungen + Google Sheets 1-basiert
                      if (data.sheetName === CONFIG.SHEETS.PLAKATE && actualColIndex === 3) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in"
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${actualColIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.AUFBAU && actualColIndex > 1) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in"
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${actualColIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.KUCHEN && actualColIndex === 3) {
                        // Dropdown für Tag in Kuchenspenden - actualColIndex 3 = Spalte C (Tag)
                        const options = ['', 'Fr', 'Sa', 'So'];
                        return `<td>
                          <div class="input-field">
                            <select onchange="updateListe('${data.sheetName}', ${rowIndex}, ${actualColIndex}, this.value)">
                              ${options.map(option =>
                                `<option value="${option}" ${cell === option ? 'selected' : ''}>${option || 'Tag wählen'}</option>`
                              ).join('')}
                            </select>
                          </div>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}"
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${actualColIndex}, this.value)">
                          </div>
                        </td>`;
                      }
                    }
                  }).join('');
                  return tr.outerHTML;
                }).join('')}
                ${listType === 'vorMarkt' ? `
                  <tr class="new-entry-row">
                    ${columnConfig.map((col, colIndex) => {
                      const isCheckbox = (data.sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) ||
                                        (data.sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0);

                      if (isCheckbox) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in"
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.KUCHEN && colIndex === 1) {
                        // Dropdown für Tag in Kuchenspenden (neue Zeile) - colIndex 1 = Tag-Spalte
                        const options = ['', 'Fr', 'Sa', 'So'];
                        return `<td>
                          <div class="input-field">
                            <select onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                              ${options.map(option =>
                                `<option value="${option}">${option || 'Tag wählen'}</option>`
                              ).join('')}
                            </select>
                          </div>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text"
                                   placeholder="${col.placeholder || 'Name eingeben'}"
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                          </div>
                        </td>`;
                      }
                    }).join('')}
                  </tr>
                ` : ''}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;

    // Initialisiere Materialize Select-Elemente nach dem Laden der Liste
    setTimeout(() => {
      const selectElements = document.querySelectorAll('select');
      if (selectElements.length > 0) {
        M.FormSelect.init(selectElements);
      }
    }, 100);
  }

  // Generische Funktion zum Hinzufügen neuer Einträge
  function addNewEntry(sheetName, input, colIndex) {
    const row = input.closest('tr');
    
    // Sammle alle Eingabeelemente in der richtigen Reihenfolge
    const allInputs = row.querySelectorAll('input, select');
    const allValues = Array.from(allInputs).map(element => {
      if (element.type === 'checkbox') {
        return element.checked ? 'X' : '';
      }
      return element.value;
    });

    // Prüfe, ob die aktuelle Zeile die letzte ist
    const tbody = row.parentNode;
    const isLastRow = row === tbody.lastElementChild;

    // Wenn mindestens ein Feld ausgefüllt wurde UND es ist die letzte Zeile
    if (allValues.some(value => value.trim() !== '') && isLastRow) {
      // Markiere diese Zeile als "nicht mehr neu" durch Entfernen der Klasse
      row.classList.remove('new-entry-row');

      // Ändere die onchange Events zu updateListe für diese Zeile
      const currentRowIndex = Array.from(tbody.children).indexOf(row);
      Array.from(allInputs).forEach((element, index) => {
        // Berechne die korrekte Spalte: index (0-basiert) + 2 (für Google Sheets 1-basiert + übersprungene Nr-Spalte)
        const correctColumnIndex = index + 2;
        if (element.type === 'checkbox') {
          element.setAttribute('onchange', `updateListe('${sheetName}', ${currentRowIndex}, ${correctColumnIndex}, this.checked ? 'X' : '')`);
        } else {
          element.setAttribute('onchange', `updateListe('${sheetName}', ${currentRowIndex}, ${correctColumnIndex}, this.value)`);
        }
      });

      // Erstelle eine neue leere Zeile
      const newRow = document.createElement('tr');
      newRow.className = 'new-entry-row';
      newRow.innerHTML = row.innerHTML;

      // Setze leere Werte in die neue Zeile
      const newAllInputs = newRow.querySelectorAll('input, select');
      Array.from(newAllInputs).forEach((element, index) => {
        if (element.type === 'checkbox') {
          element.checked = false;
          element.setAttribute('onchange', `addNewEntry('${sheetName}', this, ${index})`);
        } else if (element.type === 'select-one') {
          element.value = '';
          element.setAttribute('onchange', `addNewEntry('${sheetName}', this, ${index})`);
        } else {
          element.value = '';
          element.setAttribute('onchange', `addNewEntry('${sheetName}', this, ${index})`);
        }
      });

      tbody.appendChild(newRow);

      // Initialisiere Materialize Select-Elemente in der neuen Zeile
      const selectElements = newRow.querySelectorAll('select');
      if (selectElements.length > 0) {
        M.FormSelect.init(selectElements);
      }

      // Einfache Nummerierung: Anzahl der Zeilen in der Tabelle (vor dem Hinzufügen der neuen Zeile)
      const nextNumber = tbody.children.length - 1;
      const valuesWithNumber = [nextNumber, ...allValues];

      // Speichere die Änderungen
      google.script.run
        .withSuccessHandler(function() {
          // Erfolgreich gespeichert
          M.toast({html: 'Eintrag gespeichert', classes: 'success'});
          // Invalidiere den Cache für diese Liste
          invalidateCacheForSheet(sheetName);
        })
        .withFailureHandler(function(error) {
          console.error('Fehler beim Hinzufügen:', error);
          showError('Fehler beim Hinzufügen: ' + error);
        })
        .addEintrag(sheetName, valuesWithNumber);
    }
  }
  
  // Update-Funktion
  function updateListe(sheetName, rowIndex, columnIndex, value) {
    google.script.run
      .withSuccessHandler(function(success) {
        if (success) {
          M.toast({html: 'Änderung gespeichert', classes: 'success'});
          invalidateCacheForSheet(sheetName);
        } else {
          M.toast({html: 'Fehler beim Speichern', classes: 'error'});
        }
      })
      .withFailureHandler(function(error) {
        M.toast({html: 'Fehler: ' + error, classes: 'error'});
      })
      .updateListe(sheetName, rowIndex, columnIndex, value);
  }
</script> 